# Overview

This is a full-stack web application for Techlabs, a software product engineering company. The project showcases the company's expertise in AI, data engineering, enterprise applications, electronics engineering, and product design. Built as a modern React SPA with an Express.js backend, it features a sophisticated corporate website with sections for company information, expertise areas, project showcases, careers, and contact information.

# User Preferences

Preferred communication style: Simple, everyday language.

# System Architecture

## Frontend Architecture
- **React SPA** with TypeScript for type safety
- **Vite** as the build tool and development server
- **Wouter** for client-side routing instead of React Router
- **TanStack Query** for server state management and data fetching
- **Tailwind CSS** with CSS variables for theming
- **shadcn/ui** component library with Radix UI primitives
- **Custom CSS** with dark theme design system

## Backend Architecture
- **Express.js** server with TypeScript
- **RESTful API** structure with `/api` prefix
- **Custom middleware** for request logging and error handling
- **Memory storage** implementation with interface-based design for easy database migration
- **Session handling** prepared with connect-pg-simple for PostgreSQL sessions

## Database Layer
- **Drizzle ORM** configured for PostgreSQL with Neon Database serverless driver
- **Schema-first approach** with TypeScript types generated from database schema
- **Zod validation** integrated with Drizzle schemas
- **Migration system** ready with drizzle-kit

## Component Architecture
- **Modular section-based components** (hero, about, expertise, work, careers, etc.)
- **Shared UI components** following shadcn/ui patterns
- **Responsive design** with mobile-first approach
- **Icon system** using Lucide React icons
- **Form handling** with React Hook Form and Zod validation

## Development Workflow
- **Hot module replacement** in development with Vite
- **TypeScript strict mode** with path mapping for imports
- **ESModules** throughout the application
- **Build optimization** with separate client and server builds
- **Error boundaries** and development error overlays

## Deployment Strategy
- **Production build** creates optimized client bundle and bundled server
- **Static asset serving** handled by Express in production
- **Environment-based configuration** for development vs production

# External Dependencies

## Database & ORM
- **Neon Database** - Serverless PostgreSQL database
- **Drizzle ORM** - Type-safe database toolkit
- **connect-pg-simple** - PostgreSQL session store

## UI & Styling
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Low-level UI primitives for accessibility
- **shadcn/ui** - High-quality component system
- **Lucide React** - Icon library
- **class-variance-authority** - Component variant management

## Development Tools
- **Vite** - Fast build tool and development server
- **TypeScript** - Static type checking
- **ESBuild** - Fast JavaScript bundler for production
- **PostCSS** - CSS processing with Tailwind

## Runtime Libraries
- **React** - UI library with hooks
- **Express.js** - Web server framework
- **TanStack Query** - Server state management
- **React Hook Form** - Form handling
- **Zod** - Schema validation
- **date-fns** - Date utility library
- **Wouter** - Lightweight router

## Replit Integration
- **@replit/vite-plugin-runtime-error-modal** - Development error handling
- **@replit/vite-plugin-cartographer** - Development tooling (conditional)