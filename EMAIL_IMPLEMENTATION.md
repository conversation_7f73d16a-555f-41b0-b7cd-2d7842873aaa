# Email Implementation for Static Hosting

This document explains how the email functionality has been implemented for the TechLabs website to work with static hosting (cPanel/shared hosting) without requiring a Node.js backend.

## Solution Overview

We've implemented **EmailJS** - a client-side email service that allows sending emails directly from the browser without a backend server. This is perfect for static hosting environments.

## How It Works

### Work With Us Form:
1. **User fills out the form** → Form data is collected in React state
2. **Form submission** → EmailJS sends the inquiry data directly to your email
3. **No backend required** → Everything runs in the browser

### Job Application Form:
1. **User fills out the form and uploads CV** → Form data and file are collected
2. **File processing** → CV is converted to base64 for email attachment
3. **Form submission** → EmailJS sends the application data with CV attachment
4. **Email delivery** → You receive the application with the CV as an attachment

**Static hosting compatible** → Both forms work on any hosting provider

## Files Modified/Created

### New Files:
- `client/src/config/email.ts` - EmailJS configuration
- `client/src/services/emailService.ts` - Email sending service
- `client/src/components/email-test.tsx` - Test component (remove after testing)
- `EMAILJS_SETUP.md` - Detailed setup instructions
- `.env.example` - Environment variables template

### Modified Files:
- `client/src/components/work-with-us-form.tsx` - Updated to use EmailJS instead of backend API
- `client/src/components/job-application-form.tsx` - Updated to use EmailJS with file attachment support

## Quick Setup Steps

1. **Install EmailJS** (already done):
   ```bash
   npm install @emailjs/browser
   ```

2. **Create EmailJS Account**:
   - Go to https://www.emailjs.com/
   - Sign up and verify email
   - Set up email service (Gmail recommended)
   - Create email template
   - Get Service ID, Template ID, and Public Key

3. **Configure the Application**:
   
   **Option A: Direct configuration** (quick setup):
   Edit `client/src/config/email.ts`:
   ```typescript
   export const emailConfig = {
     serviceId: 'service_your_id_here',
     templates: {
       workWithUs: 'template_work_id_here',
       jobApplication: 'template_job_id_here',
     },
     publicKey: 'your_public_key_here',
     targetEmail: '<EMAIL>',
   };
   ```

   **Option B: Environment variables** (recommended):
   ```bash
   cp .env.example .env
   # Edit .env with your actual EmailJS credentials
   ```

4. **Test the Setup**:
   - Add the test component to your main page temporarily
   - Test email sending
   - Remove test component after verification

## Email Template

Use this template in your EmailJS dashboard:

**Subject**: `New Work Inquiry from {{from_name}}`

**Body**:
```
Hello,

You have received a new work inquiry through your website:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Interest: {{interest}}

Details:
{{inquiry_details}}

Best regards,
TechLabs Website
```

## Benefits of This Approach

✅ **No backend required** - Perfect for static hosting
✅ **No server costs** - Runs entirely in the browser  
✅ **Easy deployment** - Just upload built files to any hosting
✅ **Reliable** - EmailJS handles delivery and retries
✅ **Secure** - No sensitive credentials in client code
✅ **Free tier available** - 200 emails/month on free plan

## Deployment

### For cPanel/Shared Hosting:
1. Run `npm run build`
2. Upload `dist` folder contents to your hosting
3. Emails will work immediately after EmailJS setup

### For Other Static Hosts:
- **Netlify**: Set environment variables in dashboard
- **Vercel**: Set environment variables in dashboard  
- **GitHub Pages**: Use direct configuration method

## Security Notes

- ✅ Public Key is safe to expose in client code
- ✅ Service ID and Template ID are safe to expose
- ❌ Never expose Private Key in client code
- ✅ EmailJS automatically prevents spam/abuse

## Troubleshooting

**Common issues:**
- Check browser console for errors
- Verify all IDs are correct in EmailJS dashboard
- Check spam folder for test emails
- Ensure email service is properly configured in EmailJS

**Rate limits:**
- Free: 200 emails/month
- Paid plans available for higher volumes

## Next Steps

1. Follow the detailed setup guide in `EMAILJS_SETUP.md`
2. Test the email functionality
3. Deploy to your static hosting provider
4. Remove the test component after verification

The form will now send emails directly to `<EMAIL>` whenever someone submits a work inquiry!
