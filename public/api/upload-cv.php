<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Check if file was uploaded
    if (!isset($_FILES['cv']) || $_FILES['cv']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error occurred');
    }

    $file = $_FILES['cv'];
    $applicantName = $_POST['applicantName'] ?? 'unknown';
    $fileName = $_POST['fileName'] ?? $file['name'];

    // Validate file type
    $allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    $fileType = $file['type'];
    
    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('Invalid file type. Only PDF, DOC, and DOCX files are allowed.');
    }

    // Validate file size (max 10MB)
    $maxSize = 10 * 1024 * 1024; // 10MB
    if ($file['size'] > $maxSize) {
        throw new Exception('File size exceeds 10MB limit');
    }

    // Create uploads directory if it doesn't exist
    $uploadDir = '../uploads/cvs/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $timestamp = time();
    $sanitizedName = preg_replace('/[^a-zA-Z0-9]/', '_', $applicantName);
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $uniqueFileName = "cv_{$sanitizedName}_{$timestamp}.{$extension}";
    
    $uploadPath = $uploadDir . $uniqueFileName;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('Failed to save uploaded file');
    }

    // Generate download URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $fileUrl = "{$protocol}://{$host}/uploads/cvs/{$uniqueFileName}";

    // Log the upload (optional)
    $logEntry = date('Y-m-d H:i:s') . " - CV uploaded: {$uniqueFileName} by {$applicantName}\n";
    file_put_contents('../uploads/upload_log.txt', $logEntry, FILE_APPEND | LOCK_EX);

    // Return success response
    echo json_encode([
        'success' => true,
        'fileUrl' => $fileUrl,
        'fileName' => $uniqueFileName,
        'originalName' => $file['name'],
        'size' => $file['size']
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
