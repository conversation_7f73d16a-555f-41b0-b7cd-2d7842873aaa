import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>au<PERSON>, <PERSON>ap, Globe } from "lucide-react";

export default function CustomerSection() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6" data-testid="text-why-partner">Why Partner with Techlabs?</h2>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <Settings className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4">Deep Expertise</h3>
            <p className="text-muted-foreground">Our multidisciplinary teams blend software and hardware expertise.</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <Gauge className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4">Agility at Scale</h3>
            <p className="text-muted-foreground">From startups to Fortune 500, we adapt engineering to your growth journey.</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <Zap className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4">Innovation Edge</h3>
            <p className="text-muted-foreground">Early adoption of AI, IoT, and GenAI ensures your solutions stay ahead.</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <Globe className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4">Global Mindset</h3>
            <p className="text-muted-foreground">Delivering world-class engineering for UK, European, and global markets.</p>
          </div>
        </div>
        
        <div className="text-center">
          <Button 
            size="lg"
            onClick={() => scrollToSection('contact')}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary/25 transform hover:-translate-y-1"
            data-testid="button-start-project"
          >
            Start Your Project With Us
          </Button>
        </div>
      </div>
    </section>
  );
}
