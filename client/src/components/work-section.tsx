import { Badge } from "@/components/ui/badge";
import kayaLogo from "@assets/KAYA logo_1757051363039.png";
import divolgoLogo from "@assets/512x512bb_1757056222618.png";

import kayaImage from "@assets/KAYA.png";
import divolgoImage from "@assets/Divolgo image.png";
import onlineGameImage from "@assets/online_game.jpg";

export default function WorkSection() {
  return (
    <section id="work" className="py-20 bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white" data-testid="text-our-work">Our Work</h2>
          <p className="text-lg text-white/80">
            We let our projects speak for our capabilities:
          </p>
        </div>
        
        <div className="space-y-12">
          {/* KAYA Project */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <img 
                src={kayaLogo} 
                alt="KAYA Logo" 
                className="h-8 w-auto mb-4"
                data-testid="img-kaya-logo"
              />
              <p className="text-lg text-muted-foreground mb-6">
                KAYA empowers enterprises to achieve seamless operations by harmonizing workflows through AI/ML driven automation. Our platform integrates effortlessly with existing systems, allowing teams to focus on strategic initiatives without the disruption of fragmented tools. <a 
                  href="https://www.kayatech.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary/80 transition-colors duration-200"
                  data-testid="link-kaya-website"
                >
                  Learn more about KAYA (www.kayatech.com)
                </a>
              </p>
              <div className="flex flex-wrap gap-2 mb-6">
                <Badge variant="secondary" className="bg-primary/20 text-primary">Agentic AI</Badge>
                <Badge variant="secondary" className="bg-primary/20 text-primary">Enterprise</Badge>
                <Badge variant="secondary" className="bg-primary/20 text-primary">Fortune 500</Badge>
              </div>
            </div>
            {/* AI technology workspace with data visualization screens */}
            <div className="relative">
              <img 
                src={kayaImage}
                alt="AI technology workspace with data visualization screens" 
                className="rounded-xl shadow-lg w-full h-auto" 
                data-testid="img-kaya-workspace"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent rounded-xl"></div>
            </div>
          </div>
          
          {/* Gaming Infrastructure */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Modern server room with network infrastructure */}
            <div className="relative lg:order-1">
              <img 
                src={onlineGameImage}
                alt="Modern server room with network infrastructure" 
                className="rounded-xl shadow-lg w-full h-auto" 
                data-testid="img-server-infrastructure"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent rounded-xl"></div>
            </div>
            <div className="lg:order-2">
              <h3 className="text-2xl font-bold mb-4 text-gradient" data-testid="text-gaming-title">Online Gaming Platform</h3>
              <p className="text-lg text-muted-foreground mb-6">
                Techlabs services team powers one of the world's leading online gaming platforms. Our engineers help develop innovative solutions including chatbots that use natural language processing to serve millions of users globally, everyday.
              </p>
              <div className="flex flex-wrap gap-2 mb-6">
                <Badge variant="secondary" className="bg-primary/20 text-primary">Gaming Platform</Badge>
                <Badge variant="secondary" className="bg-primary/20 text-primary">Global Scale</Badge>
                <Badge variant="secondary" className="bg-primary/20 text-primary">Infrastructure</Badge>
              </div>
            </div>
          </div>
          
          {/* Ad-Tech Wearable */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <img 
                src={divolgoLogo} 
                alt="Divolgo Logo" 
                className="h-12 w-auto mb-4"
                data-testid="img-divolgo-logo"
              />
              <p className="text-lg text-muted-foreground mb-6">
                A groundbreaking solution combining electronics engineering, product design, and software auction platforms to redefine how brands connect with audiences through wearable technology.
              </p>
              <div className="flex flex-wrap gap-2 mb-6">
                <Badge variant="secondary" className="bg-primary/20 text-primary">Wearable Tech</Badge>
                <Badge variant="secondary" className="bg-primary/20 text-primary">Ad-Tech</Badge>
                <Badge variant="secondary" className="bg-primary/20 text-primary">IoT</Badge>
              </div>
            </div>
            {/* Innovative wearable technology and electronics design */}
            <div className="relative">
              <img 
                src={divolgoImage}
                alt="Innovative wearable technology and electronics design" 
                className="rounded-xl shadow-lg w-full h-auto" 
                data-testid="img-wearable-tech"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent rounded-xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
