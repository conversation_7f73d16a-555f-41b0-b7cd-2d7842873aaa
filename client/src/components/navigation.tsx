import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import techlabsLogo from "@assets/techlabs logo_1757046277255.png";

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
    setIsMenuOpen(false);
  };

  return (
    <nav className={`fixed top-0 w-full z-50 nav-blur border-b border-border transition-shadow duration-200 ${isScrolled ? 'shadow-lg' : ''}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <img 
              src={techlabsLogo} 
              alt="Techlabs Logo" 
              className="h-8 w-auto"
              data-testid="nav-logo"
            />
          </div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-8">
              <button 
                onClick={() => scrollToSection('about')} 
                className="text-white/80 hover:text-white transition-colors duration-200"
                data-testid="nav-about"
              >
                About
              </button>
              <button 
                onClick={() => scrollToSection('expertise')} 
                className="text-white/80 hover:text-white transition-colors duration-200"
                data-testid="nav-expertise"
              >
                Expertise
              </button>
              <button 
                onClick={() => scrollToSection('work')} 
                className="text-white/80 hover:text-white transition-colors duration-200"
                data-testid="nav-work"
              >
                Our Work
              </button>
              <button 
                onClick={() => scrollToSection('careers')} 
                className="text-white/80 hover:text-white transition-colors duration-200"
                data-testid="nav-careers"
              >
                Careers
              </button>
              <button 
                onClick={() => scrollToSection('contact')} 
                className="text-white/80 hover:text-white transition-colors duration-200"
                data-testid="nav-contact"
              >
                Contact
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button 
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-white hover:text-white/80 transition-colors duration-200"
              data-testid="button-mobile-menu"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden" style={{background: 'linear-gradient(135deg, hsla(217, 91%, 60%, 0.95) 0%, hsla(142, 76%, 36%, 0.95) 100%)'}}>
          <div className="px-2 pt-2 pb-3 space-y-1">
            <button 
              onClick={() => scrollToSection('about')}
              className="block px-3 py-2 text-white/80 hover:text-white transition-colors duration-200 w-full text-left"
              data-testid="mobile-nav-about"
            >
              About
            </button>
            <button 
              onClick={() => scrollToSection('expertise')}
              className="block px-3 py-2 text-white/80 hover:text-white transition-colors duration-200 w-full text-left"
              data-testid="mobile-nav-expertise"
            >
              Expertise
            </button>
            <button 
              onClick={() => scrollToSection('work')}
              className="block px-3 py-2 text-white/80 hover:text-white transition-colors duration-200 w-full text-left"
              data-testid="mobile-nav-work"
            >
              Our Work
            </button>
            <button 
              onClick={() => scrollToSection('careers')}
              className="block px-3 py-2 text-white/80 hover:text-white transition-colors duration-200 w-full text-left"
              data-testid="mobile-nav-careers"
            >
              Careers
            </button>
            <button 
              onClick={() => scrollToSection('contact')}
              className="block px-3 py-2 text-white/80 hover:text-white transition-colors duration-200 w-full text-left"
              data-testid="mobile-nav-contact"
            >
              Contact
            </button>
          </div>
        </div>
      )}
    </nav>
  );
}
