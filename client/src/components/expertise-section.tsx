import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, CheckCircle } from "lucide-react";

export default function ExpertiseSection() {
  return (
    <section id="expertise" className="py-20 bg-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white" data-testid="text-our-expertise">Our Expertise</h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            We deliver end-to-end product engineering solutions across four pillars:
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
          {/* AI & GenAI */}
          <div className="card-hover card-gradient p-8 rounded-xl border-0">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                <Brain className="text-white h-6 w-6" />
              </div>
              <h3 className="text-2xl font-bold text-white">Artificial Intelligence & GenAI</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Agentic AI platforms</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">NLP-powered applications</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">AI-driven process automation</span>
              </div>
            </div>
          </div>
          
          {/* Enterprise Applications */}
          <div className="card-hover card-gradient p-8 rounded-xl border-0">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                <Server className="text-white h-6 w-6" />
              </div>
              <h3 className="text-2xl font-bold text-white">Enterprise Applications & Data Engineering</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Large-scale application development</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">High-performance data platforms</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Cloud-native architecture</span>
              </div>
            </div>
          </div>
          
          {/* Electronics Engineering */}
          <div className="card-hover card-gradient p-8 rounded-xl border-0">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                <Cpu className="text-white h-6 w-6" />
              </div>
              <h3 className="text-2xl font-bold text-white">Electronics Engineering & Product Design</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">IoT solutions</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Wearable technology</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Integrated hardware-software innovation</span>
              </div>
            </div>
          </div>
          
          {/* Product Incubation */}
          <div className="card-hover card-gradient p-8 rounded-xl border-0">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                <Rocket className="text-white h-6 w-6" />
              </div>
              <h3 className="text-2xl font-bold text-white">Product Incubation & Scaling</h3>
            </div>
            <div className="space-y-3">
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">From concept to global scale</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Strategic technology partnerships</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="text-white mr-3 h-5 w-5" />
                <span className="text-white/90">Continuous innovation cycles</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
