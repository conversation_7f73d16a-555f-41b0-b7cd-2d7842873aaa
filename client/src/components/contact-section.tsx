import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Mail, Globe as GlobeIcon } from "lucide-react";

interface ContactSectionProps {
  onOpenJobForm: () => void;
  onOpenWorkForm: () => void;
}

export default function ContactSection({ onOpenJobForm, onOpenWorkForm }: ContactSectionProps) {
  return (
    <section id="contact" className="py-20 bg-card">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6" data-testid="text-ready-to-build">Ready to build the future together?</h2>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <MapPin className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Location</h3>
            <p className="text-muted-foreground">Techlabs Global Pvt. Ltd.<br/>9B Horton Place, Colombo 7, Sri Lanka</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <Mail className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Email</h3>
            <div className="space-y-1">
              <a 
                href="mailto:<EMAIL>" 
                className="block text-primary hover:text-primary/80 transition-colors duration-200"
                data-testid="link-email-contact"
              >
                <EMAIL>
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="block text-primary hover:text-primary/80 transition-colors duration-200"
                data-testid="link-email-hr"
              >
                <EMAIL>
              </a>
            </div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
              <GlobeIcon className="text-primary h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Website</h3>
            <a 
              href="https://www.techlabs.tech" 
              className="text-primary hover:text-primary/80 transition-colors duration-200"
              target="_blank"
              rel="noopener noreferrer"
              data-testid="link-website"
            >
              www.techlabs.tech
            </a>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button 
            size="lg"
            onClick={onOpenWorkForm}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary/25 transform hover:-translate-y-1"
            data-testid="button-work-with-us"
          >
            Work With Us
          </Button>
          <Button 
            variant="outline"
            size="lg"
            onClick={onOpenJobForm}
            className="border border-blue-500 bg-blue-500/20 hover:bg-blue-500/30 text-blue-600 hover:text-blue-500 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/25 transform hover:-translate-y-1"
            data-testid="button-join-team-footer"
          >
            Join Our Team
          </Button>
        </div>
      </div>
    </section>
  );
}
