import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { emailService } from "@/services/emailService";
import { useToast } from "@/hooks/use-toast";

// Simple test component to verify EmailJS setup
// Remove this component after testing
export default function EmailTest() {
  const [isTesting, setIsTesting] = useState(false);
  const [isTestingJob, setIsTestingJob] = useState(false);
  const { toast } = useToast();

  const testWorkWithUsEmail = async () => {
    setIsTesting(true);

    try {
      await emailService.sendWorkWithUsEmail({
        name: "Test User",
        email: "<EMAIL>",
        phone: "+1234567890",
        interest: "new-project"
      });

      toast({
        title: "Work with us test email sent!",
        description: "Check <EMAIL> for the test email.",
      });
    } catch (error) {
      toast({
        title: "Test failed",
        description: error instanceof Error ? error.message : "EmailJS setup needs configuration.",
        variant: "destructive"
      });
    } finally {
      setIsTesting(false);
    }
  };

  const testJobApplicationEmail = async () => {
    setIsTestingJob(true);

    try {
      // Create a dummy PDF file for testing
      const dummyContent = "This is a test CV file content";
      const blob = new Blob([dummyContent], { type: 'application/pdf' });
      const testFile = new File([blob], 'test-cv.pdf', { type: 'application/pdf' });

      await emailService.sendJobApplicationEmail({
        name: "Test Applicant",
        email: "<EMAIL>",
        phone: "+1234567890",
        role: "Software Engineer"
      }, testFile);

      toast({
        title: "Job application test email sent!",
        description: "Check <EMAIL> for the test email with CV attachment.",
      });
    } catch (error) {
      toast({
        title: "Job application test failed",
        description: error instanceof Error ? error.message : "EmailJS setup needs configuration.",
        variant: "destructive"
      });
    } finally {
      setIsTestingJob(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200 space-y-3">
      <h3 className="font-semibold text-yellow-800 mb-2">EmailJS Test</h3>
      <p className="text-sm text-yellow-700 mb-3">
        Click to test if EmailJS is configured correctly. Remove this component after testing.
      </p>
      <div className="flex gap-2">
        <Button
          onClick={testWorkWithUsEmail}
          disabled={isTesting}
          variant="outline"
          size="sm"
        >
          {isTesting ? "Sending..." : "Test Work With Us"}
        </Button>
        <Button
          onClick={testJobApplicationEmail}
          disabled={isTestingJob}
          variant="outline"
          size="sm"
        >
          {isTestingJob ? "Sending..." : "Test Job Application"}
        </Button>
      </div>
    </div>
  );
}
