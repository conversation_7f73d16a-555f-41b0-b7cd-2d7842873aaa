import { useState } from "react";
import { Button } from "@/components/ui/button";
import { emailService } from "@/services/emailService";
import { useToast } from "@/hooks/use-toast";

// Simple test component to verify EmailJS setup
// Remove this component after testing
export default function EmailTest() {
  const [isTesting, setIsTesting] = useState(false);
  const { toast } = useToast();

  const testEmail = async () => {
    setIsTesting(true);
    
    try {
      await emailService.sendWorkWithUsEmail({
        name: "Test User",
        email: "<EMAIL>",
        phone: "+1234567890",
        interest: "new-project"
      });
      
      toast({
        title: "Test email sent!",
        description: "Check <EMAIL> for the test email.",
      });
    } catch (error) {
      toast({
        title: "Test failed",
        description: error instanceof Error ? error.message : "EmailJS setup needs configuration.",
        variant: "destructive"
      });
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
      <h3 className="font-semibold text-yellow-800 mb-2">EmailJS Test</h3>
      <p className="text-sm text-yellow-700 mb-3">
        Click to test if EmailJS is configured correctly. Remove this component after testing.
      </p>
      <Button 
        onClick={testEmail} 
        disabled={isTesting}
        variant="outline"
        size="sm"
      >
        {isTesting ? "Sending..." : "Test Email"}
      </Button>
    </div>
  );
}
