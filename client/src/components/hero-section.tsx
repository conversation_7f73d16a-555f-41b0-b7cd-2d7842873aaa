import { But<PERSON> } from "@/components/ui/button";
import { Rocket, Users } from "lucide-react";

interface HeroSectionProps {
  onOpenJobForm: () => void;
}

export default function HeroSection({ onOpenJobForm }: HeroSectionProps) {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with overlay */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 gradient-bg z-10"></div>
        <div className="hero-pattern absolute inset-0 z-20"></div>
      </div>
      
      <div className="relative z-30 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight text-white">
            Empowering the Enterprise with Digital Intelligence
          </h1>
          
          <p className="text-lg md:text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
            Techlabs is your trusted product engineering partner, delivering cutting-edge solutions in AI, enterprise applications, data engineering, electronics, and product design. From global platforms to groundbreaking startups, we incubate, scale, and power the technology of tomorrow.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              size="lg"
              onClick={() => scrollToSection('work')}
              className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border border-white/30 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:shadow-lg hover:shadow-white/25 transform hover:-translate-y-1"
              data-testid="button-explore-solutions"
            >
              <Rocket className="mr-2 h-5 w-5" />
              Explore Our Solutions
            </Button>
            <Button 
              variant="outline"
              size="lg"
              onClick={onOpenJobForm}
              className="border border-green-500 bg-green-500/20 hover:bg-green-500/30 text-green-400 hover:text-green-300 px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:shadow-lg hover:shadow-green-500/25 transform hover:-translate-y-1"
              data-testid="button-join-team"
            >
              <Users className="mr-2 h-5 w-5" />
              Join Our Team
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
