import { Presentation, Puzzle, Flask<PERSON>onical, Quote } from "lucide-react";

export default function CultureSection() {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6" data-testid="text-techlabs-way">The Techlabs Way</h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            We believe in blending experience with young energy. Our team is a dynamic mix of industry veterans and emerging engineers, united by curiosity, creativity, and a passion for solving hard problems.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="text-center card-hover bg-gray-800 p-8 rounded-xl border-0">
            <div className="w-16 h-16 mx-auto mb-6 bg-white/20 rounded-full flex items-center justify-center">
              <Presentation className="text-white h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-white">Mentorship that matters</h3>
            <p className="text-white/80">Real guidance from industry leaders.</p>
          </div>
          
          <div className="text-center card-hover bg-gray-800 p-8 rounded-xl border-0">
            <div className="w-16 h-16 mx-auto mb-6 bg-white/20 rounded-full flex items-center justify-center">
              <Puzzle className="text-white h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-white">Collaborative DNA</h3>
            <p className="text-white/80">Ideas thrive in open, agile teams.</p>
          </div>
          
          <div className="text-center card-hover bg-gray-800 p-8 rounded-xl border-0">
            <div className="w-16 h-16 mx-auto mb-6 bg-white/20 rounded-full flex items-center justify-center">
              <FlaskConical className="text-white h-8 w-8" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-white">Innovation-first culture</h3>
            <p className="text-white/80">Experimentation encouraged.</p>
          </div>
        </div>
        
        <div className="bg-slate-900 p-8 rounded-xl border-0 max-w-4xl mx-auto">
          <div className="text-center">
            <Quote className="text-blue-500 h-12 w-12 mx-auto mb-6" />
            <blockquote className="text-xl italic mb-6 text-blue-400" data-testid="text-employee-quote">
              "At Techlabs, I learned more in six months than I did in years of study. Every project pushes me to grow."
            </blockquote>
            <p className="font-semibold text-white">– Software Engineer</p>
          </div>
        </div>
      </div>
    </section>
  );
}
