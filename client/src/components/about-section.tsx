import { Lightbulb, Users, Globe } from "lucide-react";
import whoWeAre from "@assets/Who We are.png";

export default function AboutSection() {
  return (
    <section id="about" className="py-20 bg-card">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6" data-testid="text-who-we-are">Who We Are</h2>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              At Techlabs, we live at the edge of possibility.
            </p>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Our DNA fuses AI, data, software, electronics, and product design into powerful platforms that scale fast and solve hard problems.
            </p>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              We partner with innovators across the US, the UK and Europe to harness GenAI, natural language intelligence, next-gen wearables, and advanced data ecosystems—building the kind of tech that doesn't just follow trends, but creates them.
            </p>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed font-semibold">
              Engineering with vision. Innovation with speed.
            </p>
          </div>
          
          {/* Software engineering team working collaboratively */}
          <div className="relative">
            <img 
              src={whoWeAre} 
              alt="Team of professionals based in India collaborating around a laptop" 
              className="rounded-xl shadow-lg w-full h-auto" 
              data-testid="img-team-collaboration"
            />
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent rounded-xl"></div>
          </div>
        </div>
        
        <div className="mt-20">
          <h3 className="text-2xl font-bold mb-12 text-center" data-testid="text-our-dna">Our DNA</h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center card-hover bg-background p-8 rounded-xl border border-border">
              <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
                <Lightbulb className="text-primary h-8 w-8" />
              </div>
              <h4 className="text-xl font-semibold mb-4">Innovation-first</h4>
              <p className="text-muted-foreground">Harnessing emerging technologies for real-world impact.</p>
            </div>
            
            <div className="text-center card-hover bg-background p-8 rounded-xl border border-border">
              <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
                <Users className="text-primary h-8 w-8" />
              </div>
              <h4 className="text-xl font-semibold mb-4">People-powered</h4>
              <p className="text-muted-foreground">Experienced engineers and ambitious young talent working side by side.</p>
            </div>
            
            <div className="text-center card-hover bg-background p-8 rounded-xl border border-border">
              <div className="w-16 h-16 mx-auto mb-6 bg-primary/20 rounded-full flex items-center justify-center">
                <Globe className="text-primary h-8 w-8" />
              </div>
              <h4 className="text-xl font-semibold mb-4">Global impact</h4>
              <p className="text-muted-foreground">Solutions that serve millions of users worldwide.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
