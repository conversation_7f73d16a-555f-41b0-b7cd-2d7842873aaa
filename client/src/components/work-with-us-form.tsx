import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Mail } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { emailService, type WorkWithUsFormData } from "@/services/emailService";

interface WorkWithUsFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function WorkWithUsForm({ isOpen, onClose }: WorkWithUsFormProps) {
  const [formData, setFormData] = useState<WorkWithUsFormData>({
    name: "",
    phone: "",
    email: "",
    interest: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      interest: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.phone || !formData.email || !formData.interest) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Send email using EmailJS
      await emailService.sendWorkWithUsEmail(formData);

      toast({
        title: "Inquiry submitted!",
        description: "Thank you for your interest. We'll get back to you soon.",
      });

      // Reset form
      setFormData({ name: "", phone: "", email: "", interest: "" });
      onClose();
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Submission failed",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-background rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold" data-testid="text-work-with-us">Work With Us</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
              data-testid="button-close-work-form"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Full Name *</Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter your full name"
                required
                data-testid="input-work-name"
              />
            </div>

            <div>
              <Label htmlFor="phone">Contact Number *</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="Enter your contact number"
                required
                data-testid="input-work-phone"
              />
            </div>

            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email address"
                required
                data-testid="input-work-email"
              />
            </div>

            <div>
              <Label htmlFor="interest">I am interested in *</Label>
              <Select onValueChange={handleSelectChange} value={formData.interest}>
                <SelectTrigger data-testid="select-interest">
                  <SelectValue placeholder="Please select your interest" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new-project">A new project</SelectItem>
                  <SelectItem value="partnership">Potential Partnership</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="flex-1"
                data-testid="button-cancel-work"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-primary hover:bg-primary/90"
                data-testid="button-submit-work"
              >
                <Mail className="mr-2 h-4 w-4" />
                {isSubmitting ? "Submitting..." : "Submit Inquiry"}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}