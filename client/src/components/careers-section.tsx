import { <PERSON><PERSON> } from "@/components/ui/button";
import { GraduationCap, Rocket, Globe, TrendingUp, Brain, Lightbulb, Handshake } from "lucide-react";
import future from "@assets/future.png";

export default function CareersSection() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section id="careers" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-16">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6" data-testid="text-build-future">Build the Future. Together.</h2>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              At Techlabs, engineering is more than coding—it's about creating impactful solutions for the world.
            </p>
            
            <div className="space-y-6">
              <h3 className="text-2xl font-bold">Why Join Us?</h3>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <GraduationCap className="text-primary mr-4 mt-1 h-6 w-6" />
                  <div>
                    <h4 className="font-semibold mb-1">Learn from the Best</h4>
                    <p className="text-muted-foreground">Work shoulder-to-shoulder with senior engineers with decades of experience.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Rocket className="text-primary mr-4 mt-1 h-6 w-6" />
                  <div>
                    <h4 className="font-semibold mb-1">Access Cutting-Edge Tech</h4>
                    <p className="text-muted-foreground">Experiment with GenAI, data engineering, IoT, and next-gen product platforms.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Globe className="text-primary mr-4 mt-1 h-6 w-6" />
                  <div>
                    <h4 className="font-semibold mb-1">Global Exposure</h4>
                    <p className="text-muted-foreground">Collaborate with world-class clients and projects.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <TrendingUp className="text-primary mr-4 mt-1 h-6 w-6" />
                  <div>
                    <h4 className="font-semibold mb-1">Accelerated Growth</h4>
                    <p className="text-muted-foreground">Our internships, scholarships, and mentorship programs are designed to help you level up.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Modern collaborative workspace with diverse team */}
          <div className="relative">
            <img 
              src={future}
              alt="Modern collaborative workspace with diverse team" 
              className="rounded-xl shadow-lg w-full h-auto" 
              data-testid="img-collaborative-workspace"
            />
            <div className="absolute inset-0 bg-gradient-to-tr from-primary/20 to-transparent rounded-xl"></div>
          </div>
        </div>
        
        <div className="card-gradient p-8 rounded-xl border-0">
          <h3 className="text-2xl font-bold mb-6 text-white" data-testid="text-who-we-look-for">Who We Look For</h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <Brain className="text-white h-12 w-12 mx-auto mb-4" />
              <p className="text-white/90">Engineers with intelligence, energy, and a thirst for learning.</p>
            </div>
            <div className="text-center">
              <Lightbulb className="text-white h-12 w-12 mx-auto mb-4" />
              <p className="text-white/90">Innovators who want to push boundaries in software and hardware.</p>
            </div>
            <div className="text-center">
              <Handshake className="text-white h-12 w-12 mx-auto mb-4" />
              <p className="text-white/90">Team players who thrive in a dynamic, fast-paced environment.</p>
            </div>
          </div>
        </div>
        
        <div className="text-center mt-12">
          <Button 
            size="lg"
            onClick={() => scrollToSection('contact')}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary/25 transform hover:-translate-y-1"
            data-testid="button-explore-careers"
          >
            Explore Careers at Techlabs
          </Button>
        </div>
      </div>
    </section>
  );
}
