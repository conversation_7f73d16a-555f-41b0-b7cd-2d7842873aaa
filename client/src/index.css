@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 98%);
  --foreground: hsl(0, 0%, 15%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(0, 0%, 15%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(0, 0%, 15%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(0, 0%, 96%);
  --secondary-foreground: hsl(0, 0%, 15%);
  --muted: hsl(0, 0%, 96%);
  --muted-foreground: hsl(0, 0%, 45%);
  --accent: hsl(0, 0%, 96%);
  --accent-foreground: hsl(0, 0%, 15%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --border: hsl(0, 0%, 90%);
  --input: hsl(0, 0%, 96%);
  --ring: hsl(217, 91%, 60%);
  --chart-1: hsl(217, 91%, 60%);
  --chart-2: hsl(142, 76%, 36%);
  --chart-3: hsl(42, 92%, 56%);
  --chart-4: hsl(147, 78%, 42%);
  --chart-5: hsl(341, 75%, 51%);
  --sidebar: hsl(0, 0%, 100%);
  --sidebar-foreground: hsl(0, 0%, 15%);
  --sidebar-primary: hsl(217, 91%, 60%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(0, 0%, 96%);
  --sidebar-accent-foreground: hsl(0, 0%, 15%);
  --sidebar-border: hsl(0, 0%, 90%);
  --sidebar-ring: hsl(217, 91%, 60%);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --shadow-xs: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), 0px 1px 3px 0px rgba(0, 0, 0, 0.1);
  --shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1), 0px 1px 2px 0px rgba(0, 0, 0, 0.06);
  --shadow-md: 0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0px 10px 15px -3px rgba(0, 0, 0, 0.1), 0px 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0px 20px 25px -5px rgba(0, 0, 0, 0.1), 0px 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0px 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(0, 0%, 4%);
  --foreground: hsl(0, 0%, 98%);
  --card: hsl(0, 0%, 8%);
  --card-foreground: hsl(0, 0%, 98%);
  --popover: hsl(0, 0%, 8%);
  --popover-foreground: hsl(0, 0%, 98%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(0, 0%, 4%);
  --secondary: hsl(0, 0%, 14%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --muted: hsl(0, 0%, 14%);
  --muted-foreground: hsl(0, 0%, 65%);
  --accent: hsl(0, 0%, 14%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62%, 50%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --border: hsl(0, 0%, 14%);
  --input: hsl(0, 0%, 14%);
  --ring: hsl(217, 91%, 60%);
  --sidebar: hsl(0, 0%, 8%);
  --sidebar-foreground: hsl(0, 0%, 98%);
  --sidebar-primary: hsl(217, 91%, 60%);
  --sidebar-primary-foreground: hsl(0, 0%, 4%);
  --sidebar-accent: hsl(0, 0%, 14%);
  --sidebar-accent-foreground: hsl(0, 0%, 98%);
  --sidebar-border: hsl(0, 0%, 14%);
  --sidebar-ring: hsl(217, 91%, 60%);
}

@layer base {
  * {
    @apply border-border;
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .gradient-bg {
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(142, 76%, 36%) 100%);
  }

  .tech-gradient {
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(142, 76%, 36%) 100%);
  }

  .card-gradient {
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(142, 76%, 36%) 100%);
  }

  .hero-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.1) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  }

  .text-gradient {
    background: linear-gradient(135deg, hsl(217, 91%, 60%), hsl(142, 76%, 36%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .nav-blur {
    backdrop-filter: blur(12px);
    background: linear-gradient(135deg, hsla(217, 91%, 60%, 0.9) 0%, hsla(142, 76%, 36%, 0.9) 100%);
    border-bottom: 1px solid hsla(255, 255, 255, 0.2);
  }

  .dark .gradient-bg {
    background: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(142, 76%, 36%) 100%);
  }

  .dark .hero-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .dark .card-hover:hover {
    box-shadow: 0 25px 50px -12px rgba(255, 255, 255, 0.1);
  }

  .dark .nav-blur {
    background: linear-gradient(135deg, hsla(217, 91%, 60%, 0.9) 0%, hsla(142, 76%, 36%, 0.9) 100%);
    border-bottom: 1px solid hsla(255, 255, 255, 0.2);
  }
}
