// EmailJS Configuration
// You need to set up an EmailJS account and get these values:
// 1. Go to https://www.emailjs.com/
// 2. Create an account and verify your email
// 3. Create an email service (Gmail, Outlook, etc.)
// 4. Create an email template
// 5. Get your Public Key from the Integration page

export const emailConfig = {
  // Replace these with your actual EmailJS credentials
  serviceId: 'service_6ox5w7f', // e.g., 'service_abc123'

  // Email templates
  templates: {
    workWithUs: 'template_kz80efp', // Work with us inquiries
    jobApplication: 'template_ctdlkhr', // Job applications (you can create a separate template if needed)
  },

  publicKey: 'XUSfcHBx8YFFwyfcl', // e.g., 'user_abcdef123456'

  // Target email address
  targetEmail: '<EMAIL>',
};

// Environment-specific configuration
export const getEmailConfig = () => {
  // In production, you might want to use environment variables
  // For static hosting, you can set these directly or use build-time variables
  return {
    serviceId: import.meta.env.VITE_EMAILJS_SERVICE_ID || emailConfig.serviceId,
    templates: {
      workWithUs: import.meta.env.VITE_EMAILJS_WORK_TEMPLATE_ID || emailConfig.templates.workWithUs,
      jobApplication: import.meta.env.VITE_EMAILJS_JOB_TEMPLATE_ID || emailConfig.templates.jobApplication,
    },
    publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY || emailConfig.publicKey,
  };
};
