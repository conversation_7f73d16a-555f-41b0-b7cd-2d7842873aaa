// EmailJS Configuration
// You need to set up an EmailJS account and get these values:
// 1. Go to https://www.emailjs.com/
// 2. Create an account and verify your email
// 3. Create an email service (Gmail, Outlook, etc.)
// 4. Create an email template
// 5. Get your Public Key from the Integration page

export const emailConfig = {
  // Replace these with your actual EmailJS credentials
  serviceId: 'service_6ox5w7f', // e.g., 'service_abc123'
  templateId: 'template_kz80efp', // e.g., 'template_xyz789'
  publicKey: 'XUSfcHBx8YFFwyfcl', // e.g., 'user_abcdef123456'

  // Target email address
  targetEmail: '<EMAIL>',
};

// Environment-specific configuration
export const getEmailConfig = () => {
  // In production, you might want to use environment variables
  // For static hosting, you can set these directly or use build-time variables
  return {
    serviceId: import.meta.env.VITE_EMAILJS_SERVICE_ID || emailConfig.serviceId,
    templateId: import.meta.env.VITE_EMAILJS_TEMPLATE_ID || emailConfig.templateId,
    publicKey: import.meta.env.VITE_EMAILJS_PUBLIC_KEY || emailConfig.publicKey,
  };
};
