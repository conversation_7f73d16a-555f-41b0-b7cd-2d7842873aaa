import { useState } from "react";
import Navigation from "@/components/navigation";
import HeroSection from "@/components/hero-section";
import AboutSection from "@/components/about-section";
import ExpertiseSection from "@/components/expertise-section";
import WorkSection from "@/components/work-section";
import CustomerSection from "@/components/customer-section";
import CareersSection from "@/components/careers-section";
import CultureSection from "@/components/culture-section";
import ContactSection from "@/components/contact-section";
import Footer from "@/components/footer";
import JobApplicationForm from "@/components/job-application-form";
import WorkWithUsForm from "@/components/work-with-us-form";

export default function Home() {
  const [isJobFormOpen, setIsJobFormOpen] = useState(false);
  const [isWorkFormOpen, setIsWorkFormOpen] = useState(false);

  const handleOpenJobForm = () => {
    setIsJobFormOpen(true);
  };

  const handleCloseJobForm = () => {
    setIsJobFormOpen(false);
  };

  const handleOpenWorkForm = () => {
    setIsWorkFormOpen(true);
  };

  const handleCloseWorkForm = () => {
    setIsWorkFormOpen(false);
  };

  return (
    <div className="min-h-screen bg-background text-foreground font-sans antialiased">
      <Navigation />
      <HeroSection onOpenJobForm={handleOpenJobForm} />
      <AboutSection />
      <WorkSection />
      <ExpertiseSection />
      <CustomerSection />
      <CareersSection />
      <CultureSection />
      <ContactSection onOpenJobForm={handleOpenJobForm} onOpenWorkForm={handleOpenWorkForm} />
      <Footer />
      <JobApplicationForm 
        isOpen={isJobFormOpen} 
        onClose={handleCloseJobForm} 
      />
      <WorkWithUsForm 
        isOpen={isWorkFormOpen} 
        onClose={handleCloseWorkForm} 
      />
    </div>
  );
}
