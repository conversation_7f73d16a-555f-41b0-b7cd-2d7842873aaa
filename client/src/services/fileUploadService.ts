// File upload service for CV files
export interface UploadResponse {
  success: boolean;
  fileUrl?: string;
  fileName?: string;
  error?: string;
}

export class FileUploadService {
  private static instance: FileUploadService;

  private constructor() {}

  public static getInstance(): FileUploadService {
    if (!FileUploadService.instance) {
      FileUploadService.instance = new FileUploadService();
    }
    return FileUploadService.instance;
  }

  // Upload CV file to server
  public async uploadCV(file: File, applicantName: string): Promise<UploadResponse> {
    try {
      // Create a unique filename
      const timestamp = Date.now();
      const sanitizedName = applicantName.replace(/[^a-zA-Z0-9]/g, '_');
      const fileExtension = file.name.split('.').pop();
      const uniqueFileName = `cv_${sanitizedName}_${timestamp}.${fileExtension}`;

      // Create FormData for upload
      const formData = new FormData();
      formData.append('cv', file);
      formData.append('fileName', uniqueFileName);
      formData.append('applicantName', applicantName);

      // Upload to your server (adjust URL based on your deployment)
      const response = await fetch('/api/upload-cv.php', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        return {
          success: true,
          fileUrl: result.fileUrl,
          fileName: result.fileName,
        };
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('File upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  // Alternative: Upload to cloud storage (if you have access)
  public async uploadToCloudinary(file: File, applicantName: string): Promise<UploadResponse> {
    try {
      // This would require Cloudinary setup
      const cloudName = 'your_cloud_name'; // Replace with your Cloudinary cloud name
      const uploadPreset = 'cv_uploads'; // Replace with your upload preset
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', uploadPreset);
      formData.append('public_id', `cvs/${applicantName}_${Date.now()}`);

      const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/raw/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Cloudinary upload failed');
      }

      const result = await response.json();
      
      return {
        success: true,
        fileUrl: result.secure_url,
        fileName: file.name,
      };
    } catch (error) {
      console.error('Cloudinary upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Cloud upload failed',
      };
    }
  }
}

export const fileUploadService = FileUploadService.getInstance();
