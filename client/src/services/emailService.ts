import emailjs from '@emailjs/browser';
import { getEmailConfig, emailConfig } from '../config/email';

export interface WorkWithUsFormData {
  name: string;
  phone: string;
  email: string;
  interest: string;
}

export class EmailService {
  private static instance: EmailService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    const config = getEmailConfig();
    
    // Initialize EmailJS with your public key
    emailjs.init(config.publicKey);
    this.isInitialized = true;
  }

  public async sendWorkWithUsEmail(formData: WorkWithUsFormData): Promise<void> {
    await this.initialize();

    const config = getEmailConfig();
    
    // Prepare template parameters
    const templateParams = {
      to_email: emailConfig.targetEmail,
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone,
      interest: formData.interest === 'new-project' ? 'A new project' : 'Potential Partnership',
      message: `New work inquiry from ${formData.name}`,
      // Additional formatted message for better email content
      inquiry_details: `
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Interest: ${formData.interest === 'new-project' ? 'A new project' : 'Potential Partnership'}

This inquiry was submitted through the TechLabs website contact form.
      `.trim(),
    };

    try {
      const response = await emailjs.send(
        config.serviceId,
        config.templateId,
        templateParams
      );

      if (response.status !== 200) {
        throw new Error(`EmailJS responded with status: ${response.status}`);
      }

      console.log('Email sent successfully:', response);
    } catch (error) {
      console.error('Failed to send email:', error);
      throw new Error('Failed to send email. Please try again later.');
    }
  }
}

// Export a singleton instance
export const emailService = EmailService.getInstance();
