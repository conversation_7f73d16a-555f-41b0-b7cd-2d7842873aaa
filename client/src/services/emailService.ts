import emailjs from '@emailjs/browser';
import { getEmailConfig, emailConfig } from '../config/email';

export interface WorkWithUsFormData {
  name: string;
  phone: string;
  email: string;
  interest: string;
}

export interface JobApplicationFormData {
  name: string;
  phone: string;
  email: string;
  role: string;
}

export class EmailService {
  private static instance: EmailService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    const config = getEmailConfig();
    
    // Initialize EmailJS with your public key
    emailjs.init(config.publicKey);
    this.isInitialized = true;
  }

  public async sendWorkWithUsEmail(formData: WorkWithUsFormData): Promise<void> {
    await this.initialize();

    const config = getEmailConfig();
    
    // Prepare template parameters
    const templateParams = {
      to_email: emailConfig.targetEmail,
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone,
      interest: formData.interest === 'new-project' ? 'A new project' : 'Potential Partnership',
      message: `New work inquiry from ${formData.name}`,
      // Additional formatted message for better email content
      inquiry_details: `
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Interest: ${formData.interest === 'new-project' ? 'A new project' : 'Potential Partnership'}

This inquiry was submitted through the TechLabs website contact form.
      `.trim(),
    };

    try {
      const response = await emailjs.send(
        config.serviceId,
        config.templates.workWithUs,
        templateParams
      );

      if (response.status !== 200) {
        throw new Error(`EmailJS responded with status: ${response.status}`);
      }

      console.log('Email sent successfully:', response);
    } catch (error) {
      console.error('Failed to send email:', error);
      throw new Error('Failed to send email. Please try again later.');
    }
  }

  public async sendJobApplicationEmail(formData: JobApplicationFormData, cvFile: File): Promise<void> {
    await this.initialize();

    const config = getEmailConfig();

    // Check file size limit for EmailJS (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (cvFile.size > maxSize) {
      throw new Error(`File size (${(cvFile.size / 1024 / 1024).toFixed(2)}MB) exceeds the 10MB limit. Please compress your CV or use a smaller file.`);
    }

    // Convert file to base64 for EmailJS attachment
    const fileBase64 = await this.fileToBase64(cvFile);

    // Prepare template parameters
    const templateParams = {
      to_email: emailConfig.targetEmail,
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone,
      role: formData.role,
      message: `New job application from ${formData.name} for ${formData.role}`,
      // Additional formatted message for better email content
      application_details: `
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Role: ${formData.role}

CV/Resume: ${cvFile.name} (${(cvFile.size / 1024 / 1024).toFixed(2)} MB)

This application was submitted through the TechLabs website careers page.
      `.trim(),
      // File attachment
      cv_attachment: fileBase64,
      cv_filename: cvFile.name,
    };

    try {
      const response = await emailjs.send(
        config.serviceId,
        config.templates.jobApplication,
        templateParams
      );

      if (response.status !== 200) {
        throw new Error(`EmailJS responded with status: ${response.status}`);
      }

      console.log('Job application email sent successfully:', response);
    } catch (error) {
      console.error('Failed to send job application email:', error);
      throw new Error('Failed to send application. Please try again later.');
    }
  }

  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
          const base64 = reader.result.split(',')[1];
          resolve(base64);
        } else {
          reject(new Error('Failed to convert file to base64'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
    });
  }
}

// Export a singleton instance
export const emailService = EmailService.getInstance();
