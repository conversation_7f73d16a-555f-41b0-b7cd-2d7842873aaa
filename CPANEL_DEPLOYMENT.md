# cPanel Deployment Guide

## 🚀 **Step-by-Step Deployment**

### **Step 1: Build Your React App**
```bash
npm run build
```

### **Step 2: Upload Files to cPanel**

#### **Upload React App:**
1. Open cPanel File Manager
2. Navigate to `public_html` folder
3. Upload ALL contents from the `dist` folder to `public_html`
   - `index.html` should be directly in `public_html`
   - `assets` folder should be in `public_html/assets`

#### **Upload PHP Files:**
1. In `public_html`, create folder: `api`
2. Upload these files to `public_html/api/`:
   - `upload-cv.php`
   - `test.php`

#### **Create Upload Directory:**
1. In `public_html`, create folder: `uploads`
2. Inside `uploads`, create folder: `cvs`
3. Set permissions for `uploads` folder to `755`

### **Final Directory Structure:**
```
public_html/
├── index.html
├── assets/
│   ├── index-[hash].js
│   ├── index-[hash].css
│   └── ...
├── api/
│   ├── upload-cv.php
│   └── test.php
├── uploads/
│   ├── cvs/
│   └── upload_log.txt (created automatically)
└── favicon.svg
```

---

## 🧪 **Testing Your Deployment**

### **Test 1: Website Loading**
1. Visit: `https://yourdomain.com`
2. Should see your TechLabs website

### **Test 2: PHP Working**
1. Visit: `https://yourdomain.com/api/test.php`
2. Should see JSON response:
```json
{
  "success": true,
  "message": "PHP is working correctly!",
  "timestamp": "2025-01-29 10:30:00",
  "server_info": {
    "php_version": "8.1.0",
    "upload_max_filesize": "64M",
    "post_max_size": "64M",
    "max_execution_time": "30"
  }
}
```

### **Test 3: File Upload**
1. Go to your website
2. Try submitting a job application with a CV
3. Check if upload works and email is sent

---

## 🔧 **Troubleshooting**

### **404 Not Found for PHP Files**

**Problem:** `https://yourdomain.com/api/upload-cv.php` returns 404

**Solutions:**
1. **Check file location:** Ensure `upload-cv.php` is in `public_html/api/`
2. **Check file permissions:** Set to 644 or 755
3. **Check PHP support:** Contact hosting provider if PHP isn't enabled

### **CORS Errors**

**Problem:** Browser blocks requests due to CORS

**Solution:** The PHP files already include CORS headers:
```php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
```

### **File Upload Errors**

**Problem:** "Failed to upload file"

**Solutions:**
1. **Check upload directory:** Ensure `public_html/uploads/cvs/` exists
2. **Check permissions:** Set `uploads` folder to 755
3. **Check PHP limits:** Increase in cPanel if needed:
   - `upload_max_filesize = 20M`
   - `post_max_size = 20M`
   - `max_execution_time = 60`

### **Email Not Sending**

**Problem:** File uploads but no email received

**Solutions:**
1. **Check EmailJS config:** Verify service ID, template ID, public key
2. **Check browser console:** Look for JavaScript errors
3. **Test EmailJS separately:** Use the test component

---

## 📱 **Alternative: Direct PHP Approach**

If you prefer a simpler approach without React build process:

### **Option A: Pure PHP Form**
Create `job-application.php` that handles both form display and submission.

### **Option B: Hybrid Approach**
Keep React for the main site, use PHP only for file uploads.

---

## 🛠️ **cPanel Configuration**

### **PHP Settings to Check:**
1. **PHP Version:** Use PHP 7.4 or higher
2. **File Upload Settings:**
   ```
   upload_max_filesize = 20M
   post_max_size = 20M
   max_execution_time = 60
   memory_limit = 128M
   ```

### **File Permissions:**
- PHP files: 644
- Directories: 755
- Upload directory: 755 (writable)

---

## 🎯 **Quick Deployment Checklist**

- [ ] Build React app (`npm run build`)
- [ ] Upload `dist` contents to `public_html`
- [ ] Create `public_html/api/` folder
- [ ] Upload PHP files to `api` folder
- [ ] Create `public_html/uploads/cvs/` folder
- [ ] Set folder permissions to 755
- [ ] Test PHP endpoint: `/api/test.php`
- [ ] Test website loading
- [ ] Test job application form
- [ ] Verify email delivery

**Once all items are checked, your CV upload system will be fully functional!**
