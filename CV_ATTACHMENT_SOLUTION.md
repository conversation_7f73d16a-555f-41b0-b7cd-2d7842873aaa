# CV Attachment Solution for Shared Hosting

## 🎯 **Problem Solved**
EmailJS free tier only allows 50KB attachments, but CVs are typically 1-5MB. This solution uploads CVs to your server and sends download links via email.

## ✅ **Solution: File Upload + Email Link**

### **How It Works:**
1. **User submits job application** → CV is uploaded to your server
2. **Server stores CV** → Returns a secure download URL
3. **Email sent via EmailJS** → Contains CV download link (not attachment)
4. **You receive email** → Click link to download CV

### **Benefits:**
- ✅ **Works with shared hosting (cPanel)**
- ✅ **No file size limits** (up to 10MB, easily expandable)
- ✅ **Uses your existing EmailJS setup**
- ✅ **Secure file storage** on your server
- ✅ **No monthly fees** beyond your hosting

---

## 📁 **Files Created**

### **Client-Side:**
- `client/src/services/fileUploadService.ts` - Handles CV uploads
- Updated `client/src/components/job-application-form.tsx` - Upload then email flow

### **Server-Side (PHP for cPanel):**
- `public/api/upload-cv.php` - Handles file uploads and storage

---

## 🚀 **Setup Instructions**

### **Step 1: Deploy to cPanel**

1. **Build your React app:**
   ```bash
   npm run build
   ```

2. **Upload to cPanel:**
   - Upload contents of `dist` folder to `public_html`
   - Upload `public/api/upload-cv.php` to `public_html/api/`

3. **Create uploads directory:**
   - In cPanel File Manager, create: `public_html/uploads/cvs/`
   - Set permissions to 755

### **Step 2: Configure PHP Upload**

The PHP script is already configured for:
- **File types:** PDF, DOC, DOCX
- **Max size:** 10MB (adjustable)
- **Security:** Validates file types and sizes
- **Logging:** Tracks all uploads

### **Step 3: Test the System**

1. **Visit your website**
2. **Submit a job application** with a CV
3. **Check your email** for the download link
4. **Click the link** to download the CV

---

## 🔧 **Customization Options**

### **Increase File Size Limit:**

Edit `public/api/upload-cv.php`:
```php
// Change from 10MB to 20MB
$maxSize = 20 * 1024 * 1024; // 20MB
```

Also update your cPanel PHP settings:
- `upload_max_filesize = 20M`
- `post_max_size = 20M`

### **Add More File Types:**

Edit `public/api/upload-cv.php`:
```php
$allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain', // Add .txt files
    'image/jpeg', // Add .jpg files
];
```

### **Secure Downloads (Optional):**

For extra security, you can:
1. Store files outside `public_html`
2. Create a download script that checks permissions
3. Add password protection

---

## 📧 **Email Template Update**

Update your EmailJS template to include the download link:

```
Subject: New Job Application from {{from_name}} for {{role}}

Hello,

You have received a new job application:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Role: {{role}}

CV/Resume: {{cv_filename}} ({{cv_size}})
Download CV: {{cv_download_url}}

Application Details:
{{application_details}}

Best regards,
TechLabs Website
```

---

## 🛡️ **Security Features**

### **Built-in Security:**
- ✅ **File type validation** - Only PDF, DOC, DOCX allowed
- ✅ **File size limits** - Prevents large uploads
- ✅ **Unique filenames** - Prevents conflicts and guessing
- ✅ **Upload logging** - Tracks all file uploads
- ✅ **Error handling** - Graceful failure management

### **Additional Security (Optional):**
- Password-protect the uploads folder
- Add IP-based access restrictions
- Implement download expiration
- Add virus scanning (if available)

---

## 📊 **File Management**

### **Uploaded Files Location:**
```
public_html/
├── uploads/
│   ├── cvs/
│   │   ├── cv_John_Doe_1640995200.pdf
│   │   ├── cv_Jane_Smith_1640995300.docx
│   │   └── ...
│   └── upload_log.txt
```

### **Cleanup (Optional):**
Create a cron job to delete old CVs:
```bash
# Delete CVs older than 30 days
find /path/to/uploads/cvs/ -name "*.pdf" -mtime +30 -delete
find /path/to/uploads/cvs/ -name "*.doc*" -mtime +30 -delete
```

---

## 🎯 **Advantages Over Other Solutions**

| Solution | File Size | Cost | Hosting | Setup |
|----------|-----------|------|---------|-------|
| **This Solution** | 10MB+ | $0 | Shared | Easy |
| EmailJS Paid | 10MB | $15/mo | Shared | Easy |
| VPS + Node.js | Unlimited | $5-10/mo | VPS | Complex |
| Cloud Storage | Unlimited | $5-20/mo | Any | Medium |

---

## 🚀 **Ready to Deploy!**

Your solution is now ready for cPanel deployment:

1. ✅ **CV uploads** work up to 10MB
2. ✅ **Email notifications** with download links
3. ✅ **Secure file storage** on your server
4. ✅ **No monthly fees** beyond hosting
5. ✅ **Easy maintenance** and monitoring

**Deploy and test - your job application system will handle large CV files perfectly!**
