# EmailJS Setup Guide

This guide will help you set up EmailJS to send emails directly from your static website without a backend server.

## Step 1: Create EmailJS Account

1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

## Step 2: Add Email Service

1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, Yahoo, etc.)
4. Follow the setup instructions for your provider
5. Note down the **Service ID** (e.g., `service_abc123`)

### For Gmail:
- You'll need to enable 2-factor authentication
- Create an App Password for EmailJS
- Use your Gmail address and the App Password

## Step 3: Create Email Templates

You need to create templates for both types of forms:

### Template 1: Work With Us Inquiries

1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use this template content:

```
Subject: New Work Inquiry from {{from_name}}

Hello,

You have received a new work inquiry through your website:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Interest: {{interest}}

Details:
{{inquiry_details}}

Best regards,
TechLabs Website
```

4. Save the template and note down the **Template ID** (e.g., `template_work123`)

### Template 2: Job Applications (with CV attachment)

1. Create another new template
2. Use this template content:

```
Subject: New Job Application from {{from_name}} for {{role}}

Hello,

You have received a new job application through your website:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Role: {{role}}

Application Details:
{{application_details}}

CV/Resume is attached to this email.

Best regards,
TechLabs Website
```

3. **Important for attachments**: In the template editor, add an attachment field:
   - Click "Add attachment"
   - Set the field name as `cv_attachment`
   - Set the filename field as `cv_filename`

4. Save the template and note down the **Template ID** (e.g., `template_job456`)

**Note**: You can use the same template for both forms if you prefer, but separate templates give you better control over the email content.

## Step 4: Get Public Key

1. Go to "Account" → "General"
2. Find your **Public Key** (e.g., `user_abcdef123456`)

## Step 5: Configure Your Application

### Option 1: Direct Configuration (Quick Setup)

Edit `client/src/config/email.ts` and replace the placeholder values:

```typescript
export const emailConfig = {
  serviceId: 'service_abc123', // Your actual Service ID
  templates: {
    workWithUs: 'template_work123', // Work with us template ID
    jobApplication: 'template_job456', // Job application template ID
  },
  publicKey: 'user_abcdef123456', // Your actual Public Key
  targetEmail: '<EMAIL>',
};
```

### Option 2: Environment Variables (Recommended for Production)

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your actual values:
   ```
   VITE_EMAILJS_SERVICE_ID=service_abc123
   VITE_EMAILJS_WORK_TEMPLATE_ID=template_work123
   VITE_EMAILJS_JOB_TEMPLATE_ID=template_job456
   VITE_EMAILJS_PUBLIC_KEY=user_abcdef123456
   ```

3. For static hosting, you may need to set these as build-time environment variables

## Step 6: Test the Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Test both forms:
   - Submit the "Work With Us" form
   - Submit a job application with a CV file
3. Check your email (<EMAIL>) for the test messages
4. Verify that the job application email includes the CV attachment
5. Check the browser console for any errors

### File Attachment Notes:
- Maximum file size: 5MB (enforced by the form)
- Supported formats: PDF, DOC, DOCX
- Files are converted to base64 and sent as email attachments
- EmailJS free plan supports attachments up to 10MB total per email

## Step 7: Deploy to Static Hosting

### For cPanel/Shared Hosting:

1. Build your application:
   ```bash
   npm run build
   ```

2. Upload the `dist` folder contents to your hosting provider
3. Make sure environment variables are set during build time

### For Netlify:

1. Set environment variables in Netlify dashboard
2. Deploy from your Git repository

### For Vercel:

1. Set environment variables in Vercel dashboard
2. Deploy from your Git repository

## Troubleshooting

### Common Issues:

1. **"User ID is required"**: Make sure your Public Key is correctly set
2. **"Service is not found"**: Check your Service ID
3. **"Template is not found"**: Check your Template ID
4. **CORS errors**: EmailJS handles CORS automatically, but make sure you're using the correct domain
5. **Emails not received**: Check spam folder, verify email service setup

### Testing:

- Use browser developer tools to check for console errors
- Test with a simple template first
- Verify all IDs are correct and active in EmailJS dashboard

### Rate Limits:

- Free plan: 200 emails/month
- Paid plans available for higher volumes

## Security Notes

- Public Key is safe to expose in client-side code
- Service ID and Template ID are also safe to expose
- Never expose your Private Key in client-side code
- EmailJS automatically prevents spam and abuse

## Support

- EmailJS Documentation: [https://www.emailjs.com/docs/](https://www.emailjs.com/docs/)
- EmailJS Support: Available through their dashboard
