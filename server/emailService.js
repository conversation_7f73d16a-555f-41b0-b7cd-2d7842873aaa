import nodemailer from 'nodemailer';
import multer from 'multer';
import path from 'path';

// Configure multer for file uploads
const storage = multer.memoryStorage();
export const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit (much higher than EmailJS)
  },
  fileFilter: (req, file, cb) => {
    // Allow PDF, DOC, DOCX files
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only PDF, DOC, and DOCX files are allowed.'));
    }
  }
});

// Gmail SMTP configuration with App Password
const createTransporter = () => {
  return nodemailer.createTransporter({
    service: 'gmail',
    auth: {
      user: process.env.GMAIL_USER, // Your Gmail address
      pass: process.env.GMAIL_APP_PASSWORD, // Your Gmail App Password (not regular password)
    },
  });
};

// Send work with us inquiry email
export const sendWorkWithUsEmail = async (formData) => {
  const transporter = createTransporter();
  
  const mailOptions = {
    from: process.env.GMAIL_USER,
    to: '<EMAIL>',
    subject: `New Work Inquiry from ${formData.name}`,
    html: `
      <h2>New Work Inquiry</h2>
      <p><strong>Name:</strong> ${formData.name}</p>
      <p><strong>Email:</strong> ${formData.email}</p>
      <p><strong>Phone:</strong> ${formData.phone}</p>
      <p><strong>Interest:</strong> ${formData.interest === 'new-project' ? 'A new project' : 'Potential Partnership'}</p>
      
      <hr>
      <p><em>This inquiry was submitted through the TechLabs website contact form.</em></p>
    `,
    replyTo: formData.email,
  };

  try {
    const result = await transporter.sendMail(mailOptions);
    console.log('Work inquiry email sent:', result.messageId);
    return result;
  } catch (error) {
    console.error('Error sending work inquiry email:', error);
    throw error;
  }
};

// Send job application email with CV attachment
export const sendJobApplicationEmail = async (formData, cvFile) => {
  const transporter = createTransporter();
  
  const mailOptions = {
    from: process.env.GMAIL_USER,
    to: '<EMAIL>',
    subject: `New Job Application from ${formData.name} for ${formData.role}`,
    html: `
      <h2>New Job Application</h2>
      <p><strong>Name:</strong> ${formData.name}</p>
      <p><strong>Email:</strong> ${formData.email}</p>
      <p><strong>Phone:</strong> ${formData.phone}</p>
      <p><strong>Role:</strong> ${formData.role}</p>
      
      <p><strong>CV/Resume:</strong> ${cvFile.originalname} (${(cvFile.size / 1024 / 1024).toFixed(2)} MB)</p>
      
      <hr>
      <p><em>This application was submitted through the TechLabs website careers page.</em></p>
    `,
    replyTo: formData.email,
    attachments: [
      {
        filename: cvFile.originalname,
        content: cvFile.buffer,
        contentType: cvFile.mimetype,
      }
    ],
  };

  try {
    const result = await transporter.sendMail(mailOptions);
    console.log('Job application email sent:', result.messageId);
    return result;
  } catch (error) {
    console.error('Error sending job application email:', error);
    throw error;
  }
};

// Test email function
export const sendTestEmail = async () => {
  const transporter = createTransporter();
  
  const mailOptions = {
    from: process.env.GMAIL_USER,
    to: '<EMAIL>',
    subject: 'TechLabs Email Service Test',
    html: `
      <h2>Email Service Test</h2>
      <p>This is a test email to verify that the Gmail SMTP configuration is working correctly.</p>
      <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
    `,
  };

  try {
    const result = await transporter.sendMail(mailOptions);
    console.log('Test email sent:', result.messageId);
    return result;
  } catch (error) {
    console.error('Error sending test email:', error);
    throw error;
  }
};
