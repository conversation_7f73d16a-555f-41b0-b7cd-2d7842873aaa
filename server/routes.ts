import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { sendEmail } from "./email";

export async function registerRoutes(app: Express): Promise<Server> {
  // Work With Us form submission
  app.post("/api/work-with-us", async (req, res) => {
    try {
      const { name, phone, email, interest } = req.body;

      if (!name || !phone || !email || !interest) {
        return res.status(400).json({ error: "All fields are required" });
      }

      const interestText = interest === "new-project" ? "A new project" : "Potential Partnership";

      const emailSent = await sendEmail({
        to: "<EMAIL>",
        from: "<EMAIL>", // This should be a verified sender in SendGrid
        subject: `New Work Inquiry: ${interestText}`,
        html: `
          <h2>New Work With Us Inquiry</h2>
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Contact Number:</strong> ${phone}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Interest:</strong> ${interestText}</p>
          <p><strong>Submitted at:</strong> ${new Date().toLocaleString()}</p>
        `,
        text: `
          New Work With Us Inquiry
          
          Name: ${name}
          Contact Number: ${phone}
          Email: ${email}
          Interest: ${interestText}
          Submitted at: ${new Date().toLocaleString()}
        `
      });

      if (emailSent) {
        res.json({ success: true });
      } else {
        res.status(500).json({ error: "Failed to send email" });
      }
    } catch (error) {
      console.error("Error processing work-with-us form:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
