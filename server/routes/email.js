import express from 'express';
import { sendWorkWithUsEmail, sendJobApplicationEmail, sendTestEmail, upload } from '../emailService.js';

const router = express.Router();

// Work with us form submission
router.post('/work-with-us', async (req, res) => {
  try {
    const { name, phone, email, interest } = req.body;
    
    // Validate required fields
    if (!name || !phone || !email || !interest) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Send email
    await sendWorkWithUsEmail({ name, phone, email, interest });
    
    res.json({
      success: true,
      message: 'Work inquiry submitted successfully'
    });
  } catch (error) {
    console.error('Work with us submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit inquiry. Please try again.'
    });
  }
});

// Job application form submission with CV upload
router.post('/job-application', upload.single('cv'), async (req, res) => {
  try {
    const { name, phone, email, role } = req.body;
    const cvFile = req.file;
    
    // Validate required fields
    if (!name || !phone || !email || !role) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    if (!cvFile) {
      return res.status(400).json({
        success: false,
        message: 'CV file is required'
      });
    }

    // Send email with CV attachment
    await sendJobApplicationEmail({ name, phone, email, role }, cvFile);
    
    res.json({
      success: true,
      message: 'Job application submitted successfully'
    });
  } catch (error) {
    console.error('Job application submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit application. Please try again.'
    });
  }
});

// Test email endpoint
router.post('/test', async (req, res) => {
  try {
    await sendTestEmail();
    res.json({
      success: true,
      message: 'Test email sent successfully'
    });
  } catch (error) {
    console.error('Test email error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test email'
    });
  }
});

export default router;
